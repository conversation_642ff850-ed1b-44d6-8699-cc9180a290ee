using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Models.Memory;

namespace DocumentCreationSystem.Services.Memory
{
    /// <summary>
    /// 记忆服务扩展
    /// </summary>
    public static class MemoryServiceExtensions
    {
        /// <summary>
        /// 注册持久化记忆服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddPersistentMemoryService(this IServiceCollection services)
        {
            services.AddSingleton<IPersistentMemoryService, PersistentMemoryService>();
            services.AddSingleton<IMemoryServiceInitializer, MemoryServiceInitializer>();
            
            return services;
        }

        /// <summary>
        /// 配置持久化记忆服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configureOptions">配置选项</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddPersistentMemoryService(this IServiceCollection services, 
            Action<MemoryServiceOptions> configureOptions)
        {
            services.Configure(configureOptions);
            services.AddSingleton<IPersistentMemoryService, PersistentMemoryService>();
            services.AddSingleton<IMemoryServiceInitializer, MemoryServiceInitializer>();
            
            return services;
        }
    }

    /// <summary>
    /// 记忆服务配置选项
    /// </summary>
    public class MemoryServiceOptions
    {
        /// <summary>
        /// 数据库路径
        /// </summary>
        public string? DatabasePath { get; set; }

        /// <summary>
        /// 缓存大小限制
        /// </summary>
        public int CacheSize { get; set; } = 1000;

        /// <summary>
        /// 是否启用自动清理
        /// </summary>
        public bool EnableAutoCleanup { get; set; } = true;

        /// <summary>
        /// 自动清理间隔（分钟）
        /// </summary>
        public int AutoCleanupIntervalMinutes { get; set; } = 60;

        /// <summary>
        /// 记忆过期时间（天）
        /// </summary>
        public int DefaultExpiryDays { get; set; } = 365;

        /// <summary>
        /// 最大记忆数量限制
        /// </summary>
        public int MaxMemoriesLimit { get; set; } = 100000;

        /// <summary>
        /// 是否启用记忆压缩
        /// </summary>
        public bool EnableCompression { get; set; } = false;

        /// <summary>
        /// 是否启用相似性检测
        /// </summary>
        public bool EnableSimilarityDetection { get; set; } = true;

        /// <summary>
        /// 相似性阈值
        /// </summary>
        public double SimilarityThreshold { get; set; } = 0.8;

        /// <summary>
        /// 是否启用自动关联发现
        /// </summary>
        public bool EnableAutoAssociationDiscovery { get; set; } = true;

        /// <summary>
        /// 关联发现阈值
        /// </summary>
        public double AssociationThreshold { get; set; } = 0.7;

        /// <summary>
        /// 是否启用学习功能
        /// </summary>
        public bool EnableLearning { get; set; } = true;

        /// <summary>
        /// 学习率
        /// </summary>
        public double LearningRate { get; set; } = 0.1;

        /// <summary>
        /// 是否启用遗忘机制
        /// </summary>
        public bool EnableForgetting { get; set; } = true;

        /// <summary>
        /// 遗忘阈值
        /// </summary>
        public double ForgettingThreshold { get; set; } = 0.1;

        /// <summary>
        /// 是否启用详细日志
        /// </summary>
        public bool EnableVerboseLogging { get; set; } = false;

        /// <summary>
        /// 备份间隔（小时）
        /// </summary>
        public int BackupIntervalHours { get; set; } = 24;

        /// <summary>
        /// 是否启用自动备份
        /// </summary>
        public bool EnableAutoBackup { get; set; } = true;

        /// <summary>
        /// 备份保留天数
        /// </summary>
        public int BackupRetentionDays { get; set; } = 30;
    }

    /// <summary>
    /// 记忆服务初始化器接口
    /// </summary>
    public interface IMemoryServiceInitializer
    {
        /// <summary>
        /// 初始化记忆服务
        /// </summary>
        /// <param name="memoryService">记忆服务实例</param>
        /// <returns>初始化任务</returns>
        Task InitializeAsync(IPersistentMemoryService memoryService);

        /// <summary>
        /// 加载初始记忆
        /// </summary>
        /// <param name="memoryService">记忆服务实例</param>
        /// <returns>加载任务</returns>
        Task LoadInitialMemoriesAsync(IPersistentMemoryService memoryService);

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        /// <param name="memoryService">记忆服务实例</param>
        /// <returns>设置任务</returns>
        Task SetupEventHandlersAsync(IPersistentMemoryService memoryService);
    }

    /// <summary>
    /// 记忆服务初始化器实现
    /// </summary>
    public class MemoryServiceInitializer : IMemoryServiceInitializer
    {
        private readonly ILogger<MemoryServiceInitializer> _logger;

        public MemoryServiceInitializer(ILogger<MemoryServiceInitializer> logger)
        {
            _logger = logger;
        }

        public async Task InitializeAsync(IPersistentMemoryService memoryService)
        {
            try
            {
                _logger.LogInformation("开始初始化记忆服务...");

                // 设置事件处理器
                await SetupEventHandlersAsync(memoryService);

                // 加载初始记忆
                await LoadInitialMemoriesAsync(memoryService);

                // 验证数据完整性
                var integrityResult = await memoryService.ValidateMemoryIntegrityAsync();
                if (!integrityResult.IsValid)
                {
                    _logger.LogWarning($"发现 {integrityResult.Issues.Count} 个数据完整性问题");
                }

                // 清理过期记忆
                var cleanedCount = await memoryService.CleanupExpiredMemoriesAsync();
                if (cleanedCount > 0)
                {
                    _logger.LogInformation($"清理了 {cleanedCount} 个过期记忆");
                }

                _logger.LogInformation("记忆服务初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记忆服务初始化失败");
                throw;
            }
        }

        public async Task LoadInitialMemoriesAsync(IPersistentMemoryService memoryService)
        {
            try
            {
                var initialMemories = CreateInitialMemories();
                if (initialMemories.Any())
                {
                    await memoryService.StoreMemoriesAsync(initialMemories);
                    _logger.LogInformation($"已加载 {initialMemories.Count} 个初始记忆");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载初始记忆失败");
                throw;
            }
        }

        public async Task SetupEventHandlersAsync(IPersistentMemoryService memoryService)
        {
            try
            {
                // 设置记忆变更事件处理器
                memoryService.MemoryChanged += OnMemoryChanged;
                memoryService.MemoryAdded += OnMemoryAdded;
                memoryService.MemoryDeleted += OnMemoryDeleted;
                memoryService.MemoryAssociationCreated += OnMemoryAssociationCreated;

                _logger.LogDebug("记忆服务事件处理器已设置");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置事件处理器失败");
                throw;
            }
        }

        private void OnMemoryChanged(object? sender, Models.Memory.MemoryChangedEventArgs e)
        {
            _logger.LogDebug($"记忆已变更: {e.MemoryId}, 类型: {e.ChangeType}");
        }

        private void OnMemoryAdded(object? sender, Models.Memory.MemoryAddedEventArgs e)
        {
            _logger.LogDebug($"记忆已添加: {e.Memory.Id}, 类型: {e.Memory.Type}");
        }

        private void OnMemoryDeleted(object? sender, Models.Memory.MemoryDeletedEventArgs e)
        {
            _logger.LogDebug($"记忆已删除: {e.MemoryId}, 原因: {e.Reason}");
        }

        private void OnMemoryAssociationCreated(object? sender, Models.Memory.MemoryAssociationCreatedEventArgs e)
        {
            _logger.LogDebug($"记忆关联已创建: {e.Association.SourceMemoryId} -> {e.Association.TargetMemoryId}");
        }

        private List<AgentMemory> CreateInitialMemories()
        {
            var memories = new List<AgentMemory>();

            // 系统知识记忆
            memories.Add(new AgentMemory
            {
                Type = MemoryType.Knowledge,
                Content = "AI Agent是一个能够感知环境、做出决策并采取行动以实现特定目标的自主软件实体。",
                Summary = "AI Agent的定义",
                Importance = 0.9,
                Tags = new List<string> { "AI", "Agent", "定义", "基础知识" },
                Source = "系统初始化",
                Metadata = new Dictionary<string, object>
                {
                    { "category", "fundamental_knowledge" },
                    { "domain", "artificial_intelligence" }
                }
            });

            memories.Add(new AgentMemory
            {
                Type = MemoryType.Knowledge,
                Content = "文档创作系统支持多种文档类型，包括小说、论文、专利交底书、SOP等。",
                Summary = "系统支持的文档类型",
                Importance = 0.8,
                Tags = new List<string> { "文档", "创作", "类型", "功能" },
                Source = "系统初始化",
                Metadata = new Dictionary<string, object>
                {
                    { "category", "system_capabilities" },
                    { "domain", "document_creation" }
                }
            });

            // 用户偏好记忆
            memories.Add(new AgentMemory
            {
                Type = MemoryType.Preference,
                Content = "用户通常喜欢详细的创作指导和步骤分解。",
                Summary = "用户偏好：详细指导",
                Importance = 0.7,
                Tags = new List<string> { "用户偏好", "指导", "详细" },
                Source = "系统初始化",
                Metadata = new Dictionary<string, object>
                {
                    { "category", "user_preference" },
                    { "preference_type", "guidance_style" }
                }
            });

            // 经验记忆
            memories.Add(new AgentMemory
            {
                Type = MemoryType.Experience,
                Content = "在创作过程中，提供结构化的大纲有助于提高创作效率和质量。",
                Summary = "结构化大纲的重要性",
                Importance = 0.8,
                Tags = new List<string> { "创作", "大纲", "结构", "效率" },
                Source = "系统初始化",
                Metadata = new Dictionary<string, object>
                {
                    { "category", "best_practice" },
                    { "domain", "writing" }
                }
            });

            // 技能记忆
            memories.Add(new AgentMemory
            {
                Type = MemoryType.Skill,
                Content = "Agent具备工具调用能力，可以使用文档生成、格式转换、内容分析等多种工具。",
                Summary = "Agent的工具调用技能",
                Importance = 0.9,
                Tags = new List<string> { "技能", "工具调用", "能力" },
                Source = "系统初始化",
                Metadata = new Dictionary<string, object>
                {
                    { "category", "agent_capability" },
                    { "skill_type", "tool_usage" }
                }
            });

            // 模式记忆
            memories.Add(new AgentMemory
            {
                Type = MemoryType.Pattern,
                Content = "用户在开始新项目时，通常会先询问模板或示例，然后逐步完善内容。",
                Summary = "用户创作行为模式",
                Importance = 0.7,
                Tags = new List<string> { "模式", "用户行为", "创作流程" },
                Source = "系统初始化",
                Metadata = new Dictionary<string, object>
                {
                    { "category", "behavior_pattern" },
                    { "pattern_type", "creation_workflow" }
                }
            });

            // 上下文记忆
            memories.Add(new AgentMemory
            {
                Type = MemoryType.Context,
                Content = "系统运行在Windows环境下，支持多种文件格式的导入导出。",
                Summary = "系统运行环境",
                Importance = 0.6,
                Tags = new List<string> { "环境", "Windows", "文件格式" },
                Source = "系统初始化",
                Metadata = new Dictionary<string, object>
                {
                    { "category", "system_context" },
                    { "context_type", "runtime_environment" }
                }
            });

            // 目标记忆
            memories.Add(new AgentMemory
            {
                Type = MemoryType.Goal,
                Content = "系统的主要目标是帮助用户高效地创作高质量的文档。",
                Summary = "系统主要目标",
                Importance = 0.95,
                Tags = new List<string> { "目标", "文档创作", "效率", "质量" },
                Source = "系统初始化",
                Metadata = new Dictionary<string, object>
                {
                    { "category", "system_goal" },
                    { "goal_type", "primary_objective" }
                }
            });

            return memories;
        }
    }
}
