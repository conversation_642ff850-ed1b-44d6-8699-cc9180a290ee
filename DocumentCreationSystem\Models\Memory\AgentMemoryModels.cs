using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models.Memory
{
    /// <summary>
    /// Agent记忆 - 统一的记忆模型
    /// </summary>
    public class AgentMemory
    {
        /// <summary>
        /// 记忆唯一标识
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 记忆类型
        /// </summary>
        public MemoryType Type { get; set; } = MemoryType.Knowledge;

        /// <summary>
        /// 记忆内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 记忆摘要
        /// </summary>
        public string? Summary { get; set; }

        /// <summary>
        /// 重要性评分 (0.0 - 1.0)
        /// </summary>
        public double Importance { get; set; } = 0.5;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }

        /// <summary>
        /// 访问次数
        /// </summary>
        public int AccessCount { get; set; } = 0;

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// 上下文ID
        /// </summary>
        public string? ContextId { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 记忆来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 记忆权重（用于排序和过滤）
        /// </summary>
        public double Weight { get; set; } = 1.0;

        /// <summary>
        /// 置信度 (0.0 - 1.0)
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 情感标签
        /// </summary>
        public EmotionalTag? EmotionalTag { get; set; }

        /// <summary>
        /// 记忆强度（影响遗忘速度）
        /// </summary>
        public double Strength { get; set; } = 1.0;

        /// <summary>
        /// 相关记忆ID列表
        /// </summary>
        public List<string> RelatedMemoryIds { get; set; } = new();

        /// <summary>
        /// 检查记忆是否已过期
        /// </summary>
        public bool IsExpired => ExpiryTime.HasValue && DateTime.Now > ExpiryTime.Value;

        /// <summary>
        /// 获取记忆年龄（天数）
        /// </summary>
        public double AgeInDays => (DateTime.Now - CreatedAt).TotalDays;

        /// <summary>
        /// 计算记忆的衰减重要性（考虑时间衰减）
        /// </summary>
        public double DecayedImportance
        {
            get
            {
                var decayFactor = Math.Exp(-AgeInDays / 365.0); // 一年衰减到约37%
                return Importance * Strength * decayFactor;
            }
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        public override string ToString()
        {
            return $"[{Type}] {Summary ?? Content.Substring(0, Math.Min(50, Content.Length))}...";
        }

        /// <summary>
        /// 更新访问信息
        /// </summary>
        public void UpdateAccess()
        {
            LastAccessedAt = DateTime.Now;
            AccessCount++;
        }

        /// <summary>
        /// 强化记忆（增加强度）
        /// </summary>
        /// <param name="reinforcement">强化量</param>
        public void Reinforce(double reinforcement)
        {
            Strength = Math.Min(2.0, Strength + reinforcement);
            UpdateAccess();
        }

        /// <summary>
        /// 弱化记忆（减少强度）
        /// </summary>
        /// <param name="weakening">弱化量</param>
        public void Weaken(double weakening)
        {
            Strength = Math.Max(0.1, Strength - weakening);
        }
    }

    /// <summary>
    /// 记忆类型枚举
    /// </summary>
    public enum MemoryType
    {
        /// <summary>
        /// 知识记忆 - 事实性信息
        /// </summary>
        Knowledge = 0,

        /// <summary>
        /// 经验记忆 - 过往经历
        /// </summary>
        Experience = 1,

        /// <summary>
        /// 偏好记忆 - 用户偏好
        /// </summary>
        Preference = 2,

        /// <summary>
        /// 技能记忆 - 技能和能力
        /// </summary>
        Skill = 3,

        /// <summary>
        /// 模式记忆 - 行为模式
        /// </summary>
        Pattern = 4,

        /// <summary>
        /// 上下文记忆 - 环境信息
        /// </summary>
        Context = 5,

        /// <summary>
        /// 目标记忆 - 目标和意图
        /// </summary>
        Goal = 6,

        /// <summary>
        /// 情感记忆 - 情感状态
        /// </summary>
        Emotional = 7,

        /// <summary>
        /// 程序记忆 - 程序性知识
        /// </summary>
        Procedural = 8,

        /// <summary>
        /// 语义记忆 - 概念和意义
        /// </summary>
        Semantic = 9,

        /// <summary>
        /// 情节记忆 - 特定事件
        /// </summary>
        Episodic = 10,

        /// <summary>
        /// 工作记忆 - 临时信息
        /// </summary>
        Working = 11,

        /// <summary>
        /// 元记忆 - 关于记忆的记忆
        /// </summary>
        Meta = 12
    }

    /// <summary>
    /// 情感标签
    /// </summary>
    public class EmotionalTag
    {
        /// <summary>
        /// 情感类型
        /// </summary>
        public EmotionType Type { get; set; } = EmotionType.Neutral;

        /// <summary>
        /// 情感强度 (0.0 - 1.0)
        /// </summary>
        public double Intensity { get; set; } = 0.5;

        /// <summary>
        /// 情感价值（正面/负面）(-1.0 到 1.0)
        /// </summary>
        public double Valence { get; set; } = 0.0;

        /// <summary>
        /// 情感描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 情感类型枚举
    /// </summary>
    public enum EmotionType
    {
        /// <summary>
        /// 中性
        /// </summary>
        Neutral,

        /// <summary>
        /// 快乐
        /// </summary>
        Joy,

        /// <summary>
        /// 悲伤
        /// </summary>
        Sadness,

        /// <summary>
        /// 愤怒
        /// </summary>
        Anger,

        /// <summary>
        /// 恐惧
        /// </summary>
        Fear,

        /// <summary>
        /// 惊讶
        /// </summary>
        Surprise,

        /// <summary>
        /// 厌恶
        /// </summary>
        Disgust,

        /// <summary>
        /// 期待
        /// </summary>
        Anticipation,

        /// <summary>
        /// 信任
        /// </summary>
        Trust,

        /// <summary>
        /// 满意
        /// </summary>
        Satisfaction,

        /// <summary>
        /// 困惑
        /// </summary>
        Confusion,

        /// <summary>
        /// 兴奋
        /// </summary>
        Excitement
    }

    /// <summary>
    /// 记忆模式 - 兼容现有模型
    /// </summary>
    public class MemoryPattern
    {
        /// <summary>
        /// 模式ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 模式类型
        /// </summary>
        public string PatternType { get; set; } = string.Empty;

        /// <summary>
        /// 模式描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 模式强度
        /// </summary>
        public double Strength { get; set; } = 0.5;

        /// <summary>
        /// 出现次数
        /// </summary>
        public int Occurrences { get; set; } = 1;

        /// <summary>
        /// 首次观察时间
        /// </summary>
        public DateTime FirstObserved { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后观察时间
        /// </summary>
        public DateTime LastObserved { get; set; } = DateTime.Now;

        /// <summary>
        /// 示例列表
        /// </summary>
        public List<string> Examples { get; set; } = new();

        /// <summary>
        /// 模式置信度
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 相关上下文
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();
    }
}
