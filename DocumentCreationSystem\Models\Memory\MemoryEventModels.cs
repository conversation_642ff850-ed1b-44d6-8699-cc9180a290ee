namespace DocumentCreationSystem.Models.Memory
{
    /// <summary>
    /// 记忆变更事件参数
    /// </summary>
    public class MemoryChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 变更的记忆ID
        /// </summary>
        public string MemoryId { get; set; } = string.Empty;

        /// <summary>
        /// 变更类型
        /// </summary>
        public MemoryChangeType ChangeType { get; set; } = MemoryChangeType.Updated;

        /// <summary>
        /// 变更前的记忆（如果适用）
        /// </summary>
        public AgentMemory? OldMemory { get; set; }

        /// <summary>
        /// 变更后的记忆
        /// </summary>
        public AgentMemory? NewMemory { get; set; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 变更原因
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 变更的字段列表
        /// </summary>
        public List<string> ChangedFields { get; set; } = new();

        /// <summary>
        /// 变更元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 记忆变更类型
    /// </summary>
    public enum MemoryChangeType
    {
        /// <summary>
        /// 创建
        /// </summary>
        Created,

        /// <summary>
        /// 更新
        /// </summary>
        Updated,

        /// <summary>
        /// 删除
        /// </summary>
        Deleted,

        /// <summary>
        /// 重要性变更
        /// </summary>
        ImportanceChanged,

        /// <summary>
        /// 访问
        /// </summary>
        Accessed,

        /// <summary>
        /// 强化
        /// </summary>
        Reinforced,

        /// <summary>
        /// 遗忘
        /// </summary>
        Forgotten
    }

    /// <summary>
    /// 记忆添加事件参数
    /// </summary>
    public class MemoryAddedEventArgs : EventArgs
    {
        /// <summary>
        /// 添加的记忆
        /// </summary>
        public AgentMemory Memory { get; set; } = new();

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 添加来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 是否自动添加
        /// </summary>
        public bool IsAutomatic { get; set; } = false;

        /// <summary>
        /// 相关上下文
        /// </summary>
        public MemoryContext? Context { get; set; }

        /// <summary>
        /// 添加元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 记忆删除事件参数
    /// </summary>
    public class MemoryDeletedEventArgs : EventArgs
    {
        /// <summary>
        /// 删除的记忆ID
        /// </summary>
        public string MemoryId { get; set; } = string.Empty;

        /// <summary>
        /// 删除的记忆（删除前的状态）
        /// </summary>
        public AgentMemory? DeletedMemory { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime DeletedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 删除原因
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 是否自动删除
        /// </summary>
        public bool IsAutomatic { get; set; } = false;

        /// <summary>
        /// 是否可恢复
        /// </summary>
        public bool IsRecoverable { get; set; } = true;

        /// <summary>
        /// 删除元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 记忆关联创建事件参数
    /// </summary>
    public class MemoryAssociationCreatedEventArgs : EventArgs
    {
        /// <summary>
        /// 创建的关联
        /// </summary>
        public MemoryAssociation Association { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否自动创建
        /// </summary>
        public bool IsAutomatic { get; set; } = false;

        /// <summary>
        /// 创建算法
        /// </summary>
        public string Algorithm { get; set; } = string.Empty;

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 创建元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 记忆反馈
    /// </summary>
    public class MemoryFeedback
    {
        /// <summary>
        /// 反馈ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 记忆ID
        /// </summary>
        public string MemoryId { get; set; } = string.Empty;

        /// <summary>
        /// 反馈类型
        /// </summary>
        public FeedbackType Type { get; set; } = FeedbackType.Positive;

        /// <summary>
        /// 反馈强度 (-1.0 到 1.0)
        /// </summary>
        public double Intensity { get; set; } = 0.0;

        /// <summary>
        /// 反馈来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 反馈描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 反馈时间
        /// </summary>
        public DateTime FeedbackAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 反馈上下文
        /// </summary>
        public MemoryContext? Context { get; set; }

        /// <summary>
        /// 反馈元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 反馈类型
    /// </summary>
    public enum FeedbackType
    {
        /// <summary>
        /// 正面反馈
        /// </summary>
        Positive,

        /// <summary>
        /// 负面反馈
        /// </summary>
        Negative,

        /// <summary>
        /// 中性反馈
        /// </summary>
        Neutral,

        /// <summary>
        /// 相关性反馈
        /// </summary>
        Relevance,

        /// <summary>
        /// 准确性反馈
        /// </summary>
        Accuracy,

        /// <summary>
        /// 有用性反馈
        /// </summary>
        Usefulness
    }

    /// <summary>
    /// 记忆上下文
    /// </summary>
    public class MemoryContext
    {
        /// <summary>
        /// 上下文ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 会话ID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string? TaskId { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 应用程序状态
        /// </summary>
        public string? ApplicationState { get; set; }

        /// <summary>
        /// 当前活动
        /// </summary>
        public string? CurrentActivity { get; set; }

        /// <summary>
        /// 环境信息
        /// </summary>
        public Dictionary<string, object> Environment { get; set; } = new();

        /// <summary>
        /// 时间上下文
        /// </summary>
        public TemporalContext? TemporalContext { get; set; }

        /// <summary>
        /// 空间上下文
        /// </summary>
        public SpatialContext? SpatialContext { get; set; }

        /// <summary>
        /// 社交上下文
        /// </summary>
        public SocialContext? SocialContext { get; set; }

        /// <summary>
        /// 设备上下文
        /// </summary>
        public DeviceContext? DeviceContext { get; set; }

        /// <summary>
        /// 自定义属性
        /// </summary>
        public Dictionary<string, object> CustomProperties { get; set; } = new();

        /// <summary>
        /// 上下文创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 时间上下文
    /// </summary>
    public class TemporalContext
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 时区
        /// </summary>
        public string TimeZone { get; set; } = string.Empty;

        /// <summary>
        /// 一天中的时间段
        /// </summary>
        public TimeOfDay TimeOfDay { get; set; } = TimeOfDay.Unknown;

        /// <summary>
        /// 星期几
        /// </summary>
        public DayOfWeek DayOfWeek { get; set; } = DayOfWeek.Sunday;

        /// <summary>
        /// 季节
        /// </summary>
        public Season Season { get; set; } = Season.Unknown;

        /// <summary>
        /// 是否工作日
        /// </summary>
        public bool IsWorkday { get; set; } = true;

        /// <summary>
        /// 是否节假日
        /// </summary>
        public bool IsHoliday { get; set; } = false;
    }

    /// <summary>
    /// 一天中的时间段
    /// </summary>
    public enum TimeOfDay
    {
        Unknown,
        EarlyMorning,   // 5-8
        Morning,        // 8-12
        Afternoon,      // 12-17
        Evening,        // 17-21
        Night,          // 21-5
        LateNight       // 0-5
    }

    /// <summary>
    /// 季节
    /// </summary>
    public enum Season
    {
        Unknown,
        Spring,
        Summer,
        Autumn,
        Winter
    }

    /// <summary>
    /// 空间上下文
    /// </summary>
    public class SpatialContext
    {
        /// <summary>
        /// 位置名称
        /// </summary>
        public string? LocationName { get; set; }

        /// <summary>
        /// 地理坐标
        /// </summary>
        public GeographicCoordinate? Coordinate { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public string? City { get; set; }

        /// <summary>
        /// 场所类型
        /// </summary>
        public string? VenueType { get; set; }

        /// <summary>
        /// 室内/室外
        /// </summary>
        public bool IsIndoor { get; set; } = true;
    }

    /// <summary>
    /// 地理坐标
    /// </summary>
    public class GeographicCoordinate
    {
        /// <summary>
        /// 纬度
        /// </summary>
        public double Latitude { get; set; } = 0.0;

        /// <summary>
        /// 经度
        /// </summary>
        public double Longitude { get; set; } = 0.0;

        /// <summary>
        /// 海拔
        /// </summary>
        public double? Altitude { get; set; }

        /// <summary>
        /// 精度（米）
        /// </summary>
        public double? Accuracy { get; set; }
    }

    /// <summary>
    /// 社交上下文
    /// </summary>
    public class SocialContext
    {
        /// <summary>
        /// 参与者列表
        /// </summary>
        public List<string> Participants { get; set; } = new();

        /// <summary>
        /// 社交场景
        /// </summary>
        public string? SocialSetting { get; set; }

        /// <summary>
        /// 交互类型
        /// </summary>
        public string? InteractionType { get; set; }

        /// <summary>
        /// 关系类型
        /// </summary>
        public Dictionary<string, string> Relationships { get; set; } = new();

        /// <summary>
        /// 群体大小
        /// </summary>
        public int GroupSize { get; set; } = 1;

        /// <summary>
        /// 是否正式场合
        /// </summary>
        public bool IsFormal { get; set; } = false;
    }

    /// <summary>
    /// 设备上下文
    /// </summary>
    public class DeviceContext
    {
        /// <summary>
        /// 设备类型
        /// </summary>
        public string? DeviceType { get; set; }

        /// <summary>
        /// 操作系统
        /// </summary>
        public string? OperatingSystem { get; set; }

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public string? ApplicationVersion { get; set; }

        /// <summary>
        /// 屏幕分辨率
        /// </summary>
        public string? ScreenResolution { get; set; }

        /// <summary>
        /// 网络类型
        /// </summary>
        public string? NetworkType { get; set; }

        /// <summary>
        /// 电池电量
        /// </summary>
        public double? BatteryLevel { get; set; }

        /// <summary>
        /// 是否连接电源
        /// </summary>
        public bool? IsPluggedIn { get; set; }

        /// <summary>
        /// 输入方法
        /// </summary>
        public List<string> InputMethods { get; set; } = new();
    }

    /// <summary>
    /// 交互数据
    /// </summary>
    public class InteractionData
    {
        /// <summary>
        /// 交互ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 交互类型
        /// </summary>
        public InteractionType Type { get; set; } = InteractionType.UserInput;

        /// <summary>
        /// 用户输入
        /// </summary>
        public string? UserInput { get; set; }

        /// <summary>
        /// Agent响应
        /// </summary>
        public string? AgentResponse { get; set; }

        /// <summary>
        /// 交互结果
        /// </summary>
        public InteractionResult Result { get; set; } = InteractionResult.Success;

        /// <summary>
        /// 用户满意度 (0.0 - 1.0)
        /// </summary>
        public double? UserSatisfaction { get; set; }

        /// <summary>
        /// 交互时长（毫秒）
        /// </summary>
        public long DurationMs { get; set; } = 0;

        /// <summary>
        /// 使用的工具
        /// </summary>
        public List<string> ToolsUsed { get; set; } = new();

        /// <summary>
        /// 交互上下文
        /// </summary>
        public MemoryContext? Context { get; set; }

        /// <summary>
        /// 交互时间
        /// </summary>
        public DateTime InteractionAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 相关记忆ID列表
        /// </summary>
        public List<string> RelatedMemoryIds { get; set; } = new();

        /// <summary>
        /// 交互元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 交互类型
    /// </summary>
    public enum InteractionType
    {
        /// <summary>
        /// 用户输入
        /// </summary>
        UserInput,

        /// <summary>
        /// 工具调用
        /// </summary>
        ToolCall,

        /// <summary>
        /// 系统通知
        /// </summary>
        SystemNotification,

        /// <summary>
        /// 错误处理
        /// </summary>
        ErrorHandling,

        /// <summary>
        /// 学习反馈
        /// </summary>
        LearningFeedback,

        /// <summary>
        /// 主动建议
        /// </summary>
        ProactiveSuggestion
    }

    /// <summary>
    /// 交互结果
    /// </summary>
    public enum InteractionResult
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success,

        /// <summary>
        /// 部分成功
        /// </summary>
        PartialSuccess,

        /// <summary>
        /// 失败
        /// </summary>
        Failure,

        /// <summary>
        /// 用户取消
        /// </summary>
        UserCancelled,

        /// <summary>
        /// 超时
        /// </summary>
        Timeout,

        /// <summary>
        /// 需要更多信息
        /// </summary>
        NeedMoreInfo
    }
}
