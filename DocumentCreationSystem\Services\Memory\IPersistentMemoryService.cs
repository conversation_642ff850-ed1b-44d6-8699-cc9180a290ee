using DocumentCreationSystem.Models.Memory;

namespace DocumentCreationSystem.Services.Memory
{
    /// <summary>
    /// 持久化记忆服务接口 - 提供完整的记忆管理能力
    /// </summary>
    public interface IPersistentMemoryService
    {
        #region 记忆存储和检索

        /// <summary>
        /// 存储记忆
        /// </summary>
        /// <param name="memory">记忆对象</param>
        /// <returns>存储任务</returns>
        Task<string> StoreMemoryAsync(AgentMemory memory);

        /// <summary>
        /// 批量存储记忆
        /// </summary>
        /// <param name="memories">记忆列表</param>
        /// <returns>存储的记忆ID列表</returns>
        Task<List<string>> StoreMemoriesAsync(IEnumerable<AgentMemory> memories);

        /// <summary>
        /// 根据ID获取记忆
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <returns>记忆对象</returns>
        Task<AgentMemory?> GetMemoryAsync(string memoryId);

        /// <summary>
        /// 批量获取记忆
        /// </summary>
        /// <param name="memoryIds">记忆ID列表</param>
        /// <returns>记忆列表</returns>
        Task<List<AgentMemory>> GetMemoriesAsync(IEnumerable<string> memoryIds);

        /// <summary>
        /// 搜索记忆
        /// </summary>
        /// <param name="query">搜索查询</param>
        /// <returns>匹配的记忆列表</returns>
        Task<List<AgentMemory>> SearchMemoriesAsync(MemorySearchQuery query);

        /// <summary>
        /// 相似性搜索
        /// </summary>
        /// <param name="content">查询内容</param>
        /// <param name="maxResults">最大结果数</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>相似记忆列表</returns>
        Task<List<SimilarMemory>> FindSimilarMemoriesAsync(string content, int maxResults = 10, double threshold = 0.7);

        /// <summary>
        /// 更新记忆
        /// </summary>
        /// <param name="memory">更新的记忆</param>
        /// <returns>更新任务</returns>
        Task UpdateMemoryAsync(AgentMemory memory);

        /// <summary>
        /// 删除记忆
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <returns>删除任务</returns>
        Task DeleteMemoryAsync(string memoryId);

        /// <summary>
        /// 批量删除记忆
        /// </summary>
        /// <param name="memoryIds">记忆ID列表</param>
        /// <returns>删除任务</returns>
        Task DeleteMemoriesAsync(IEnumerable<string> memoryIds);

        #endregion

        #region 记忆分类和组织

        /// <summary>
        /// 根据类型获取记忆
        /// </summary>
        /// <param name="type">记忆类型</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>记忆列表</returns>
        Task<List<AgentMemory>> GetMemoriesByTypeAsync(MemoryType type, int maxResults = 100);

        /// <summary>
        /// 根据标签获取记忆
        /// </summary>
        /// <param name="tags">标签列表</param>
        /// <param name="matchAll">是否匹配所有标签</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>记忆列表</returns>
        Task<List<AgentMemory>> GetMemoriesByTagsAsync(IEnumerable<string> tags, bool matchAll = false, int maxResults = 100);

        /// <summary>
        /// 根据时间范围获取记忆
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>记忆列表</returns>
        Task<List<AgentMemory>> GetMemoriesByTimeRangeAsync(DateTime startTime, DateTime endTime, int maxResults = 100);

        /// <summary>
        /// 根据重要性获取记忆
        /// </summary>
        /// <param name="minImportance">最小重要性</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>记忆列表</returns>
        Task<List<AgentMemory>> GetMemoriesByImportanceAsync(double minImportance, int maxResults = 100);

        /// <summary>
        /// 获取相关记忆
        /// </summary>
        /// <param name="memoryId">参考记忆ID</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>相关记忆列表</returns>
        Task<List<AgentMemory>> GetRelatedMemoriesAsync(string memoryId, int maxResults = 10);

        #endregion

        #region 记忆统计和分析

        /// <summary>
        /// 获取记忆统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<MemoryStatistics> GetMemoryStatisticsAsync();

        /// <summary>
        /// 分析记忆模式
        /// </summary>
        /// <param name="timeRange">分析时间范围</param>
        /// <returns>模式分析结果</returns>
        Task<MemoryPatternAnalysis> AnalyzeMemoryPatternsAsync(TimeSpan timeRange);

        /// <summary>
        /// 获取记忆趋势
        /// </summary>
        /// <param name="period">统计周期</param>
        /// <returns>趋势数据</returns>
        Task<MemoryTrend> GetMemoryTrendAsync(TimePeriod period);

        #endregion

        #region 记忆维护和优化

        /// <summary>
        /// 清理过期记忆
        /// </summary>
        /// <returns>清理的记忆数量</returns>
        Task<int> CleanupExpiredMemoriesAsync();

        /// <summary>
        /// 压缩记忆存储
        /// </summary>
        /// <returns>压缩任务</returns>
        Task CompressMemoryStorageAsync();

        /// <summary>
        /// 重建记忆索引
        /// </summary>
        /// <returns>重建任务</returns>
        Task RebuildMemoryIndexAsync();

        /// <summary>
        /// 优化记忆存储
        /// </summary>
        /// <returns>优化任务</returns>
        Task OptimizeMemoryStorageAsync();

        /// <summary>
        /// 验证记忆完整性
        /// </summary>
        /// <returns>验证结果</returns>
        Task<MemoryIntegrityResult> ValidateMemoryIntegrityAsync();

        #endregion

        #region 记忆导入导出

        /// <summary>
        /// 导出记忆
        /// </summary>
        /// <param name="filePath">导出文件路径</param>
        /// <param name="filter">过滤条件</param>
        /// <returns>导出任务</returns>
        Task ExportMemoriesAsync(string filePath, MemoryExportFilter? filter = null);

        /// <summary>
        /// 导入记忆
        /// </summary>
        /// <param name="filePath">导入文件路径</param>
        /// <param name="options">导入选项</param>
        /// <returns>导入的记忆数量</returns>
        Task<int> ImportMemoriesAsync(string filePath, MemoryImportOptions? options = null);

        /// <summary>
        /// 备份记忆数据库
        /// </summary>
        /// <param name="backupPath">备份路径</param>
        /// <returns>备份任务</returns>
        Task BackupMemoryDatabaseAsync(string backupPath);

        /// <summary>
        /// 恢复记忆数据库
        /// </summary>
        /// <param name="backupPath">备份路径</param>
        /// <returns>恢复任务</returns>
        Task RestoreMemoryDatabaseAsync(string backupPath);

        #endregion

        #region 记忆关联和图谱

        /// <summary>
        /// 创建记忆关联
        /// </summary>
        /// <param name="association">关联信息</param>
        /// <returns>创建任务</returns>
        Task CreateMemoryAssociationAsync(MemoryAssociation association);

        /// <summary>
        /// 删除记忆关联
        /// </summary>
        /// <param name="associationId">关联ID</param>
        /// <returns>删除任务</returns>
        Task DeleteMemoryAssociationAsync(string associationId);

        /// <summary>
        /// 获取记忆关联
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <returns>关联列表</returns>
        Task<List<MemoryAssociation>> GetMemoryAssociationsAsync(string memoryId);

        /// <summary>
        /// 构建记忆图谱
        /// </summary>
        /// <param name="centerMemoryId">中心记忆ID</param>
        /// <param name="depth">图谱深度</param>
        /// <returns>记忆图谱</returns>
        Task<MemoryGraph> BuildMemoryGraphAsync(string centerMemoryId, int depth = 3);

        /// <summary>
        /// 发现记忆关联
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <param name="threshold">关联阈值</param>
        /// <returns>发现的关联</returns>
        Task<List<MemoryAssociation>> DiscoverMemoryAssociationsAsync(string memoryId, double threshold = 0.8);

        #endregion

        #region 记忆学习和适应

        /// <summary>
        /// 从交互中学习
        /// </summary>
        /// <param name="interaction">交互数据</param>
        /// <returns>学习任务</returns>
        Task LearnFromInteractionAsync(InteractionData interaction);

        /// <summary>
        /// 更新记忆重要性
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <param name="feedback">反馈信息</param>
        /// <returns>更新任务</returns>
        Task UpdateMemoryImportanceAsync(string memoryId, MemoryFeedback feedback);

        /// <summary>
        /// 强化记忆
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <param name="reinforcement">强化信号</param>
        /// <returns>强化任务</returns>
        Task ReinforceMemoryAsync(string memoryId, double reinforcement);

        /// <summary>
        /// 遗忘不重要的记忆
        /// </summary>
        /// <param name="threshold">遗忘阈值</param>
        /// <returns>遗忘的记忆数量</returns>
        Task<int> ForgetUnimportantMemoriesAsync(double threshold = 0.1);

        #endregion

        #region 记忆上下文和情境

        /// <summary>
        /// 根据上下文获取记忆
        /// </summary>
        /// <param name="context">上下文信息</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>相关记忆列表</returns>
        Task<List<AgentMemory>> GetMemoriesByContextAsync(MemoryContext context, int maxResults = 20);

        /// <summary>
        /// 更新记忆上下文
        /// </summary>
        /// <param name="memoryId">记忆ID</param>
        /// <param name="context">新的上下文</param>
        /// <returns>更新任务</returns>
        Task UpdateMemoryContextAsync(string memoryId, MemoryContext context);

        /// <summary>
        /// 获取情境相关记忆
        /// </summary>
        /// <param name="situation">情境描述</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>相关记忆列表</returns>
        Task<List<AgentMemory>> GetSituationalMemoriesAsync(string situation, int maxResults = 15);

        #endregion

        #region 记忆事件和通知

        /// <summary>
        /// 记忆变更事件
        /// </summary>
        event EventHandler<MemoryChangedEventArgs>? MemoryChanged;

        /// <summary>
        /// 记忆添加事件
        /// </summary>
        event EventHandler<MemoryAddedEventArgs>? MemoryAdded;

        /// <summary>
        /// 记忆删除事件
        /// </summary>
        event EventHandler<MemoryDeletedEventArgs>? MemoryDeleted;

        /// <summary>
        /// 记忆关联创建事件
        /// </summary>
        event EventHandler<MemoryAssociationCreatedEventArgs>? MemoryAssociationCreated;

        #endregion
    }
}
