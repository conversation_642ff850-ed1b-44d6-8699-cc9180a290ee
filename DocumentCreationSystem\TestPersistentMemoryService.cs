using DocumentCreationSystem.Models.Memory;
using DocumentCreationSystem.Services.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 持久化记忆服务测试程序
    /// </summary>
    public class TestPersistentMemoryService
    {
        private readonly ILogger<TestPersistentMemoryService> _logger;
        private readonly IPersistentMemoryService _memoryService;
        private readonly IMemoryServiceInitializer _initializer;

        public TestPersistentMemoryService()
        {
            // 配置服务
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            services.AddPersistentMemoryService();

            var serviceProvider = services.BuildServiceProvider();
            
            _logger = serviceProvider.GetRequiredService<ILogger<TestPersistentMemoryService>>();
            _memoryService = serviceProvider.GetRequiredService<IPersistentMemoryService>();
            _initializer = serviceProvider.GetRequiredService<IMemoryServiceInitializer>();
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTestsAsync()
        {
            try
            {
                _logger.LogInformation("=== 持久化记忆服务测试开始 ===");

                // 初始化记忆服务
                await InitializeMemoryServiceAsync();

                // 测试基础存储和检索
                await TestBasicStorageAndRetrievalAsync();

                // 测试搜索功能
                await TestSearchFunctionalityAsync();

                // 测试相似性检测
                await TestSimilarityDetectionAsync();

                // 测试记忆分类
                await TestMemoryCategorizationAsync();

                // 测试记忆统计
                await TestMemoryStatisticsAsync();

                // 测试记忆清理
                await TestMemoryCleanupAsync();

                // 测试事件系统
                await TestEventSystemAsync();

                // 测试学习功能
                await TestLearningFunctionalityAsync();

                _logger.LogInformation("=== 持久化记忆服务测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "持久化记忆服务测试失败");
                throw;
            }
        }

        /// <summary>
        /// 初始化记忆服务
        /// </summary>
        private async Task InitializeMemoryServiceAsync()
        {
            _logger.LogInformation("--- 初始化记忆服务 ---");
            
            await _initializer.InitializeAsync(_memoryService);
            
            var stats = await _memoryService.GetMemoryStatisticsAsync();
            _logger.LogInformation($"初始化完成 - 记忆数量: {stats.TotalMemories}");
        }

        /// <summary>
        /// 测试基础存储和检索
        /// </summary>
        private async Task TestBasicStorageAndRetrievalAsync()
        {
            _logger.LogInformation("--- 测试基础存储和检索 ---");

            // 创建测试记忆
            var testMemory = new AgentMemory
            {
                Type = MemoryType.Experience,
                Content = "用户询问了如何创建一个科幻小说的大纲，我提供了详细的步骤指导。",
                Summary = "科幻小说大纲创建指导",
                Importance = 0.8,
                Tags = new List<string> { "科幻", "小说", "大纲", "创作指导" },
                Source = "用户交互",
                UserId = "test_user_001",
                SessionId = "session_001",
                Metadata = new Dictionary<string, object>
                {
                    { "interaction_type", "guidance_request" },
                    { "user_satisfaction", 0.9 },
                    { "response_time_ms", 1500 }
                }
            };

            // 存储记忆
            var memoryId = await _memoryService.StoreMemoryAsync(testMemory);
            _logger.LogInformation($"已存储测试记忆: {memoryId}");

            // 检索记忆
            var retrievedMemory = await _memoryService.GetMemoryAsync(memoryId);
            if (retrievedMemory != null)
            {
                _logger.LogInformation($"成功检索记忆: {retrievedMemory.Summary}");
                _logger.LogInformation($"  内容: {retrievedMemory.Content}");
                _logger.LogInformation($"  重要性: {retrievedMemory.Importance}");
                _logger.LogInformation($"  标签: {string.Join(", ", retrievedMemory.Tags)}");
                _logger.LogInformation($"  访问次数: {retrievedMemory.AccessCount}");
            }
            else
            {
                _logger.LogWarning("未能检索到测试记忆");
            }

            // 批量存储测试
            var batchMemories = new List<AgentMemory>
            {
                new AgentMemory
                {
                    Type = MemoryType.Knowledge,
                    Content = "Markdown是一种轻量级标记语言，常用于文档编写。",
                    Summary = "Markdown语言介绍",
                    Importance = 0.7,
                    Tags = new List<string> { "Markdown", "文档", "格式" },
                    Source = "知识库"
                },
                new AgentMemory
                {
                    Type = MemoryType.Preference,
                    Content = "用户偏好使用简洁的界面和快捷键操作。",
                    Summary = "用户界面偏好",
                    Importance = 0.6,
                    Tags = new List<string> { "用户偏好", "界面", "快捷键" },
                    Source = "用户行为分析"
                }
            };

            var batchIds = await _memoryService.StoreMemoriesAsync(batchMemories);
            _logger.LogInformation($"批量存储了 {batchIds.Count} 个记忆");

            // 批量检索测试
            var batchRetrieved = await _memoryService.GetMemoriesAsync(batchIds);
            _logger.LogInformation($"批量检索了 {batchRetrieved.Count} 个记忆");
        }

        /// <summary>
        /// 测试搜索功能
        /// </summary>
        private async Task TestSearchFunctionalityAsync()
        {
            _logger.LogInformation("--- 测试搜索功能 ---");

            // 关键词搜索
            var keywordQuery = new MemorySearchQuery
            {
                Keywords = "小说",
                MaxResults = 10,
                SortOrder = MemorySortOrder.ByRelevance
            };

            var keywordResults = await _memoryService.SearchMemoriesAsync(keywordQuery);
            _logger.LogInformation($"关键词搜索 '小说' 返回 {keywordResults.Count} 个结果");

            foreach (var memory in keywordResults.Take(3))
            {
                _logger.LogInformation($"  - {memory.Summary} (重要性: {memory.Importance:F2})");
            }

            // 类型搜索
            var typeQuery = new MemorySearchQuery
            {
                Type = MemoryType.Experience,
                MaxResults = 5,
                SortOrder = MemorySortOrder.ByImportance
            };

            var typeResults = await _memoryService.SearchMemoriesAsync(typeQuery);
            _logger.LogInformation($"类型搜索 'Experience' 返回 {typeResults.Count} 个结果");

            // 重要性搜索
            var importanceQuery = new MemorySearchQuery
            {
                MinImportance = 0.8,
                MaxResults = 10,
                SortOrder = MemorySortOrder.ByImportance
            };

            var importanceResults = await _memoryService.SearchMemoriesAsync(importanceQuery);
            _logger.LogInformation($"重要性搜索 (>= 0.8) 返回 {importanceResults.Count} 个结果");

            // 时间范围搜索
            var timeQuery = new MemorySearchQuery
            {
                StartTime = DateTime.Now.AddHours(-1),
                EndTime = DateTime.Now,
                MaxResults = 20,
                SortOrder = MemorySortOrder.ByTimeDesc
            };

            var timeResults = await _memoryService.SearchMemoriesAsync(timeQuery);
            _logger.LogInformation($"时间范围搜索 (最近1小时) 返回 {timeResults.Count} 个结果");
        }

        /// <summary>
        /// 测试相似性检测
        /// </summary>
        private async Task TestSimilarityDetectionAsync()
        {
            _logger.LogInformation("--- 测试相似性检测 ---");

            var queryContent = "如何写一个好的小说开头";
            var similarMemories = await _memoryService.FindSimilarMemoriesAsync(queryContent, 5, 0.3);

            _logger.LogInformation($"相似性搜索 '{queryContent}' 返回 {similarMemories.Count} 个结果");

            foreach (var similar in similarMemories)
            {
                _logger.LogInformation($"  - {similar.Memory.Summary}");
                _logger.LogInformation($"    相似度: {similar.SimilarityScore:F3}, 类型: {similar.Type}");
                _logger.LogInformation($"    内容: {similar.Memory.Content.Substring(0, Math.Min(50, similar.Memory.Content.Length))}...");
            }
        }

        /// <summary>
        /// 测试记忆分类
        /// </summary>
        private async Task TestMemoryCategorizationAsync()
        {
            _logger.LogInformation("--- 测试记忆分类 ---");

            // 按类型获取记忆
            var knowledgeMemories = await _memoryService.GetMemoriesByTypeAsync(MemoryType.Knowledge, 5);
            _logger.LogInformation($"知识类记忆: {knowledgeMemories.Count} 个");

            var experienceMemories = await _memoryService.GetMemoriesByTypeAsync(MemoryType.Experience, 5);
            _logger.LogInformation($"经验类记忆: {experienceMemories.Count} 个");

            // 按标签获取记忆
            var taggedMemories = await _memoryService.GetMemoriesByTagsAsync(new[] { "小说", "创作" }, false, 10);
            _logger.LogInformation($"包含 '小说' 或 '创作' 标签的记忆: {taggedMemories.Count} 个");

            // 按重要性获取记忆
            var importantMemories = await _memoryService.GetMemoriesByImportanceAsync(0.8, 10);
            _logger.LogInformation($"重要性 >= 0.8 的记忆: {importantMemories.Count} 个");

            foreach (var memory in importantMemories.Take(3))
            {
                _logger.LogInformation($"  - {memory.Summary} (重要性: {memory.Importance:F2})");
            }
        }

        /// <summary>
        /// 测试记忆统计
        /// </summary>
        private async Task TestMemoryStatisticsAsync()
        {
            _logger.LogInformation("--- 测试记忆统计 ---");

            var statistics = await _memoryService.GetMemoryStatisticsAsync();
            
            _logger.LogInformation($"记忆统计信息:");
            _logger.LogInformation($"  总记忆数量: {statistics.TotalMemories}");
            _logger.LogInformation($"  平均重要性: {statistics.AverageImportance:F3}");
            _logger.LogInformation($"  最高重要性: {statistics.MaxImportance:F3}");
            _logger.LogInformation($"  最低重要性: {statistics.MinImportance:F3}");
            _logger.LogInformation($"  总存储大小: {statistics.TotalStorageSize} 字节");
            _logger.LogInformation($"  平均记忆大小: {statistics.AverageMemorySize:F1} 字节");
            _logger.LogInformation($"  最近记忆数量: {statistics.RecentMemoriesCount}");
            _logger.LogInformation($"  活跃记忆数量: {statistics.ActiveMemoriesCount}");

            if (statistics.MemoriesByType.Any())
            {
                _logger.LogInformation($"  按类型分布:");
                foreach (var typeCount in statistics.MemoriesByType)
                {
                    _logger.LogInformation($"    {typeCount.Key}: {typeCount.Value} 个");
                }
            }

            if (statistics.TopTags.Any())
            {
                _logger.LogInformation($"  热门标签:");
                foreach (var tag in statistics.TopTags.Take(5))
                {
                    _logger.LogInformation($"    {tag.Key}: {tag.Value} 次");
                }
            }
        }

        /// <summary>
        /// 测试记忆清理
        /// </summary>
        private async Task TestMemoryCleanupAsync()
        {
            _logger.LogInformation("--- 测试记忆清理 ---");

            // 创建一个过期记忆
            var expiredMemory = new AgentMemory
            {
                Type = MemoryType.Context,
                Content = "这是一个临时的上下文信息，应该很快过期。",
                Summary = "临时上下文",
                Importance = 0.3,
                Tags = new List<string> { "临时", "测试" },
                Source = "测试",
                ExpiryTime = DateTime.Now.AddSeconds(-1) // 已过期
            };

            await _memoryService.StoreMemoryAsync(expiredMemory);
            _logger.LogInformation("已创建过期记忆用于测试");

            // 执行清理
            var cleanedCount = await _memoryService.CleanupExpiredMemoriesAsync();
            _logger.LogInformation($"清理了 {cleanedCount} 个过期记忆");

            // 验证数据完整性
            var integrityResult = await _memoryService.ValidateMemoryIntegrityAsync();
            _logger.LogInformation($"数据完整性验证: {(integrityResult.IsValid ? "通过" : "失败")}");
            _logger.LogInformation($"验证了 {integrityResult.ValidatedCount} 个记忆");
            
            if (integrityResult.Issues.Any())
            {
                _logger.LogInformation($"发现 {integrityResult.Issues.Count} 个问题:");
                foreach (var issue in integrityResult.Issues.Take(3))
                {
                    _logger.LogInformation($"  - {issue.Type}: {issue.Description}");
                }
            }
        }

        /// <summary>
        /// 测试事件系统
        /// </summary>
        private async Task TestEventSystemAsync()
        {
            _logger.LogInformation("--- 测试事件系统 ---");

            var eventReceived = false;

            // 订阅事件
            _memoryService.MemoryAdded += (sender, e) =>
            {
                _logger.LogInformation($"事件: 记忆已添加 - {e.Memory.Summary}");
                eventReceived = true;
            };

            _memoryService.MemoryChanged += (sender, e) =>
            {
                _logger.LogInformation($"事件: 记忆已变更 - {e.MemoryId}, 类型: {e.ChangeType}");
            };

            _memoryService.MemoryDeleted += (sender, e) =>
            {
                _logger.LogInformation($"事件: 记忆已删除 - {e.MemoryId}, 原因: {e.Reason}");
            };

            // 触发事件
            var eventTestMemory = new AgentMemory
            {
                Type = MemoryType.Experience,
                Content = "这是一个用于测试事件系统的记忆。",
                Summary = "事件测试记忆",
                Importance = 0.5,
                Tags = new List<string> { "事件", "测试" },
                Source = "事件测试"
            };

            var eventMemoryId = await _memoryService.StoreMemoryAsync(eventTestMemory);
            
            // 等待事件处理
            await Task.Delay(100);
            
            if (eventReceived)
            {
                _logger.LogInformation("事件系统工作正常");
            }
            else
            {
                _logger.LogWarning("事件系统可能存在问题");
            }

            // 更新记忆触发变更事件
            eventTestMemory.Importance = 0.7;
            await _memoryService.UpdateMemoryAsync(eventTestMemory);

            // 删除记忆触发删除事件
            await _memoryService.DeleteMemoryAsync(eventMemoryId);
        }

        /// <summary>
        /// 测试学习功能
        /// </summary>
        private async Task TestLearningFunctionalityAsync()
        {
            _logger.LogInformation("--- 测试学习功能 ---");

            // 创建交互数据
            var interactionData = new InteractionData
            {
                Type = InteractionType.UserInput,
                UserInput = "请帮我写一个关于人工智能的论文大纲",
                AgentResponse = "我为您提供了一个详细的AI论文大纲，包含引言、相关工作、方法论等章节。",
                Result = InteractionResult.Success,
                UserSatisfaction = 0.9,
                DurationMs = 2500,
                ToolsUsed = new List<string> { "论文大纲生成器", "内容结构分析器" },
                Metadata = new Dictionary<string, object>
                {
                    { "topic", "artificial_intelligence" },
                    { "document_type", "academic_paper" },
                    { "complexity_level", "advanced" }
                }
            };

            // 从交互中学习
            await _memoryService.LearnFromInteractionAsync(interactionData);
            _logger.LogInformation("已从用户交互中学习");

            // 创建记忆反馈
            var feedback = new MemoryFeedback
            {
                Type = FeedbackType.Positive,
                Intensity = 0.8,
                Source = "用户评价",
                Description = "用户对AI论文大纲的建议非常满意"
            };

            // 假设我们有一个相关的记忆ID
            var memories = await _memoryService.SearchMemoriesAsync(new MemorySearchQuery 
            { 
                Keywords = "论文", 
                MaxResults = 1 
            });

            if (memories.Any())
            {
                var memoryId = memories.First().Id;
                await _memoryService.UpdateMemoryImportanceAsync(memoryId, feedback);
                _logger.LogInformation($"已更新记忆 {memoryId} 的重要性");

                // 强化记忆
                await _memoryService.ReinforceMemoryAsync(memoryId, 0.1);
                _logger.LogInformation($"已强化记忆 {memoryId}");
            }

            // 遗忘不重要的记忆
            var forgottenCount = await _memoryService.ForgetUnimportantMemoriesAsync(0.2);
            _logger.LogInformation($"遗忘了 {forgottenCount} 个不重要的记忆");
        }

        /// <summary>
        /// 主程序入口
        /// </summary>
        public static async Task Main(string[] args)
        {
            try
            {
                var tester = new TestPersistentMemoryService();
                await tester.RunAllTestsAsync();
                
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}
