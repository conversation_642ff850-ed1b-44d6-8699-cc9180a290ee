using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models.Memory
{
    /// <summary>
    /// 记忆搜索查询
    /// </summary>
    public class MemorySearchQuery
    {
        /// <summary>
        /// 查询ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string? Keywords { get; set; }

        /// <summary>
        /// 记忆类型过滤
        /// </summary>
        public MemoryType? Type { get; set; }

        /// <summary>
        /// 标签过滤
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 最小重要性
        /// </summary>
        public double? MinImportance { get; set; }

        /// <summary>
        /// 最大重要性
        /// </summary>
        public double? MaxImportance { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 最大结果数
        /// </summary>
        public int MaxResults { get; set; } = 50;

        /// <summary>
        /// 排序方式
        /// </summary>
        public MemorySortOrder SortOrder { get; set; } = MemorySortOrder.ByRelevance;

        /// <summary>
        /// 是否包含已删除的记忆
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// 上下文过滤
        /// </summary>
        public MemoryContext? Context { get; set; }

        /// <summary>
        /// 相似度阈值
        /// </summary>
        public double SimilarityThreshold { get; set; } = 0.5;
    }

    /// <summary>
    /// 记忆排序方式
    /// </summary>
    public enum MemorySortOrder
    {
        /// <summary>
        /// 按相关性排序
        /// </summary>
        ByRelevance,

        /// <summary>
        /// 按时间排序（最新优先）
        /// </summary>
        ByTimeDesc,

        /// <summary>
        /// 按时间排序（最旧优先）
        /// </summary>
        ByTimeAsc,

        /// <summary>
        /// 按重要性排序
        /// </summary>
        ByImportance,

        /// <summary>
        /// 按访问频率排序
        /// </summary>
        ByAccessFrequency,

        /// <summary>
        /// 按相似度排序
        /// </summary>
        BySimilarity
    }

    /// <summary>
    /// 相似记忆
    /// </summary>
    public class SimilarMemory
    {
        /// <summary>
        /// 记忆对象
        /// </summary>
        public AgentMemory Memory { get; set; } = new();

        /// <summary>
        /// 相似度评分 (0.0 - 1.0)
        /// </summary>
        public double SimilarityScore { get; set; } = 0.0;

        /// <summary>
        /// 相似性类型
        /// </summary>
        public SimilarityType Type { get; set; } = SimilarityType.Content;

        /// <summary>
        /// 相似性解释
        /// </summary>
        public string Explanation { get; set; } = string.Empty;
    }

    /// <summary>
    /// 相似性类型
    /// </summary>
    public enum SimilarityType
    {
        /// <summary>
        /// 内容相似
        /// </summary>
        Content,

        /// <summary>
        /// 语义相似
        /// </summary>
        Semantic,

        /// <summary>
        /// 结构相似
        /// </summary>
        Structural,

        /// <summary>
        /// 上下文相似
        /// </summary>
        Contextual,

        /// <summary>
        /// 时间相似
        /// </summary>
        Temporal,

        /// <summary>
        /// 主题相似
        /// </summary>
        Thematic
    }

    /// <summary>
    /// 记忆统计信息
    /// </summary>
    public class MemoryStatistics
    {
        /// <summary>
        /// 统计ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 总记忆数量
        /// </summary>
        public int TotalMemories { get; set; } = 0;

        /// <summary>
        /// 按类型分组的记忆数量
        /// </summary>
        public Dictionary<MemoryType, int> MemoriesByType { get; set; } = new();

        /// <summary>
        /// 平均重要性
        /// </summary>
        public double AverageImportance { get; set; } = 0.0;

        /// <summary>
        /// 最高重要性
        /// </summary>
        public double MaxImportance { get; set; } = 0.0;

        /// <summary>
        /// 最低重要性
        /// </summary>
        public double MinImportance { get; set; } = 1.0;

        /// <summary>
        /// 总存储大小（字节）
        /// </summary>
        public long TotalStorageSize { get; set; } = 0;

        /// <summary>
        /// 平均记忆大小（字节）
        /// </summary>
        public double AverageMemorySize { get; set; } = 0.0;

        /// <summary>
        /// 最近7天新增记忆数量
        /// </summary>
        public int RecentMemoriesCount { get; set; } = 0;

        /// <summary>
        /// 活跃记忆数量（最近访问过的）
        /// </summary>
        public int ActiveMemoriesCount { get; set; } = 0;

        /// <summary>
        /// 关联数量
        /// </summary>
        public int AssociationsCount { get; set; } = 0;

        /// <summary>
        /// 平均关联度
        /// </summary>
        public double AverageAssociationDegree { get; set; } = 0.0;

        /// <summary>
        /// 最常用的标签
        /// </summary>
        public Dictionary<string, int> TopTags { get; set; } = new();

        /// <summary>
        /// 记忆访问频率分布
        /// </summary>
        public Dictionary<int, int> AccessFrequencyDistribution { get; set; } = new();

        /// <summary>
        /// 统计生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 记忆模式分析结果
    /// </summary>
    public class MemoryPatternAnalysis
    {
        /// <summary>
        /// 分析ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 分析时间范围
        /// </summary>
        public TimeSpan TimeRange { get; set; }

        /// <summary>
        /// 发现的模式列表
        /// </summary>
        public List<MemoryPattern> Patterns { get; set; } = new();

        /// <summary>
        /// 主要主题
        /// </summary>
        public List<string> MainTopics { get; set; } = new();

        /// <summary>
        /// 行为模式
        /// </summary>
        public List<BehaviorPattern> BehaviorPatterns { get; set; } = new();

        /// <summary>
        /// 时间模式
        /// </summary>
        public List<TemporalPattern> TemporalPatterns { get; set; } = new();

        /// <summary>
        /// 关联模式
        /// </summary>
        public List<AssociationPattern> AssociationPatterns { get; set; } = new();

        /// <summary>
        /// 模式置信度
        /// </summary>
        public double Confidence { get; set; } = 0.0;

        /// <summary>
        /// 分析时间
        /// </summary>
        public DateTime AnalyzedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 行为模式
    /// </summary>
    public class BehaviorPattern
    {
        /// <summary>
        /// 模式ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 模式名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模式描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 行为序列
        /// </summary>
        public List<string> BehaviorSequence { get; set; } = new();

        /// <summary>
        /// 出现频率
        /// </summary>
        public int Frequency { get; set; } = 0;

        /// <summary>
        /// 模式强度
        /// </summary>
        public double Strength { get; set; } = 0.0;

        /// <summary>
        /// 相关上下文
        /// </summary>
        public List<string> RelatedContexts { get; set; } = new();
    }

    /// <summary>
    /// 时间模式
    /// </summary>
    public class TemporalPattern
    {
        /// <summary>
        /// 模式ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 模式类型
        /// </summary>
        public TemporalPatternType Type { get; set; } = TemporalPatternType.Daily;

        /// <summary>
        /// 时间间隔
        /// </summary>
        public TimeSpan Interval { get; set; }

        /// <summary>
        /// 活跃时间段
        /// </summary>
        public List<TimeSpan> ActivePeriods { get; set; } = new();

        /// <summary>
        /// 相关活动
        /// </summary>
        public List<string> RelatedActivities { get; set; } = new();

        /// <summary>
        /// 模式强度
        /// </summary>
        public double Strength { get; set; } = 0.0;
    }

    /// <summary>
    /// 时间模式类型
    /// </summary>
    public enum TemporalPatternType
    {
        /// <summary>
        /// 每日模式
        /// </summary>
        Daily,

        /// <summary>
        /// 每周模式
        /// </summary>
        Weekly,

        /// <summary>
        /// 每月模式
        /// </summary>
        Monthly,

        /// <summary>
        /// 季节性模式
        /// </summary>
        Seasonal,

        /// <summary>
        /// 不规则模式
        /// </summary>
        Irregular
    }

    /// <summary>
    /// 关联模式
    /// </summary>
    public class AssociationPattern
    {
        /// <summary>
        /// 模式ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 关联类型
        /// </summary>
        public AssociationType Type { get; set; } = AssociationType.Semantic;

        /// <summary>
        /// 关联强度
        /// </summary>
        public double Strength { get; set; } = 0.0;

        /// <summary>
        /// 关联的概念
        /// </summary>
        public List<string> AssociatedConcepts { get; set; } = new();

        /// <summary>
        /// 关联规则
        /// </summary>
        public string Rule { get; set; } = string.Empty;

        /// <summary>
        /// 支持度
        /// </summary>
        public double Support { get; set; } = 0.0;

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; } = 0.0;
    }

    /// <summary>
    /// 关联类型
    /// </summary>
    public enum AssociationType
    {
        /// <summary>
        /// 语义关联
        /// </summary>
        Semantic,

        /// <summary>
        /// 时间关联
        /// </summary>
        Temporal,

        /// <summary>
        /// 因果关联
        /// </summary>
        Causal,

        /// <summary>
        /// 空间关联
        /// </summary>
        Spatial,

        /// <summary>
        /// 功能关联
        /// </summary>
        Functional,

        /// <summary>
        /// 结构关联
        /// </summary>
        Structural
    }

    /// <summary>
    /// 记忆趋势
    /// </summary>
    public class MemoryTrend
    {
        /// <summary>
        /// 趋势ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 统计周期
        /// </summary>
        public TimePeriod Period { get; set; } = TimePeriod.Daily;

        /// <summary>
        /// 趋势数据点
        /// </summary>
        public List<TrendDataPoint> DataPoints { get; set; } = new();

        /// <summary>
        /// 总体趋势方向
        /// </summary>
        public TrendDirection Direction { get; set; } = TrendDirection.Stable;

        /// <summary>
        /// 趋势强度
        /// </summary>
        public double Strength { get; set; } = 0.0;

        /// <summary>
        /// 预测数据点
        /// </summary>
        public List<TrendDataPoint> Predictions { get; set; } = new();

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 时间周期
    /// </summary>
    public enum TimePeriod
    {
        /// <summary>
        /// 每小时
        /// </summary>
        Hourly,

        /// <summary>
        /// 每日
        /// </summary>
        Daily,

        /// <summary>
        /// 每周
        /// </summary>
        Weekly,

        /// <summary>
        /// 每月
        /// </summary>
        Monthly,

        /// <summary>
        /// 每年
        /// </summary>
        Yearly
    }

    /// <summary>
    /// 趋势数据点
    /// </summary>
    public class TrendDataPoint
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 数值
        /// </summary>
        public double Value { get; set; } = 0.0;

        /// <summary>
        /// 标签
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 趋势方向
    /// </summary>
    public enum TrendDirection
    {
        /// <summary>
        /// 上升
        /// </summary>
        Rising,

        /// <summary>
        /// 下降
        /// </summary>
        Falling,

        /// <summary>
        /// 稳定
        /// </summary>
        Stable,

        /// <summary>
        /// 波动
        /// </summary>
        Volatile
    }

    /// <summary>
    /// 记忆完整性验证结果
    /// </summary>
    public class MemoryIntegrityResult
    {
        /// <summary>
        /// 验证ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 是否通过验证
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// 验证的记忆数量
        /// </summary>
        public int ValidatedCount { get; set; } = 0;

        /// <summary>
        /// 发现的问题
        /// </summary>
        public List<IntegrityIssue> Issues { get; set; } = new();

        /// <summary>
        /// 修复的问题数量
        /// </summary>
        public int FixedIssuesCount { get; set; } = 0;

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 验证耗时（毫秒）
        /// </summary>
        public long ValidationTimeMs { get; set; } = 0;
    }

    /// <summary>
    /// 完整性问题
    /// </summary>
    public class IntegrityIssue
    {
        /// <summary>
        /// 问题ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 问题类型
        /// </summary>
        public IntegrityIssueType Type { get; set; } = IntegrityIssueType.DataCorruption;

        /// <summary>
        /// 问题描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 受影响的记忆ID
        /// </summary>
        public string? AffectedMemoryId { get; set; }

        /// <summary>
        /// 严重程度
        /// </summary>
        public IssueSeverity Severity { get; set; } = IssueSeverity.Medium;

        /// <summary>
        /// 是否已修复
        /// </summary>
        public bool IsFixed { get; set; } = false;

        /// <summary>
        /// 修复建议
        /// </summary>
        public string? FixSuggestion { get; set; }
    }

    /// <summary>
    /// 完整性问题类型
    /// </summary>
    public enum IntegrityIssueType
    {
        /// <summary>
        /// 数据损坏
        /// </summary>
        DataCorruption,

        /// <summary>
        /// 缺失关联
        /// </summary>
        MissingAssociation,

        /// <summary>
        /// 无效引用
        /// </summary>
        InvalidReference,

        /// <summary>
        /// 重复记忆
        /// </summary>
        DuplicateMemory,

        /// <summary>
        /// 索引不一致
        /// </summary>
        IndexInconsistency,

        /// <summary>
        /// 元数据错误
        /// </summary>
        MetadataError
    }

    /// <summary>
    /// 问题严重程度
    /// </summary>
    public enum IssueSeverity
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,

        /// <summary>
        /// 中等
        /// </summary>
        Medium,

        /// <summary>
        /// 高
        /// </summary>
        High,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }

    /// <summary>
    /// 记忆导出过滤器
    /// </summary>
    public class MemoryExportFilter
    {
        /// <summary>
        /// 记忆类型过滤
        /// </summary>
        public List<MemoryType> Types { get; set; } = new();

        /// <summary>
        /// 标签过滤
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 最小重要性
        /// </summary>
        public double? MinImportance { get; set; }

        /// <summary>
        /// 时间范围开始
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 时间范围结束
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 是否包含关联
        /// </summary>
        public bool IncludeAssociations { get; set; } = true;

        /// <summary>
        /// 是否包含元数据
        /// </summary>
        public bool IncludeMetadata { get; set; } = true;

        /// <summary>
        /// 最大导出数量
        /// </summary>
        public int? MaxCount { get; set; }
    }

    /// <summary>
    /// 记忆导入选项
    /// </summary>
    public class MemoryImportOptions
    {
        /// <summary>
        /// 是否覆盖现有记忆
        /// </summary>
        public bool OverwriteExisting { get; set; } = false;

        /// <summary>
        /// 是否验证数据完整性
        /// </summary>
        public bool ValidateIntegrity { get; set; } = true;

        /// <summary>
        /// 是否创建备份
        /// </summary>
        public bool CreateBackup { get; set; } = true;

        /// <summary>
        /// 导入模式
        /// </summary>
        public ImportMode Mode { get; set; } = ImportMode.Merge;

        /// <summary>
        /// 冲突解决策略
        /// </summary>
        public ConflictResolutionStrategy ConflictResolution { get; set; } = ConflictResolutionStrategy.KeepNewer;

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 100;
    }

    /// <summary>
    /// 导入模式
    /// </summary>
    public enum ImportMode
    {
        /// <summary>
        /// 合并模式
        /// </summary>
        Merge,

        /// <summary>
        /// 替换模式
        /// </summary>
        Replace,

        /// <summary>
        /// 仅添加新记忆
        /// </summary>
        AddOnly
    }

    /// <summary>
    /// 冲突解决策略
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        /// <summary>
        /// 保留较新的
        /// </summary>
        KeepNewer,

        /// <summary>
        /// 保留较旧的
        /// </summary>
        KeepOlder,

        /// <summary>
        /// 保留重要性更高的
        /// </summary>
        KeepMoreImportant,

        /// <summary>
        /// 跳过冲突项
        /// </summary>
        Skip,

        /// <summary>
        /// 询问用户
        /// </summary>
        AskUser
    }

    /// <summary>
    /// 记忆关联
    /// </summary>
    public class MemoryAssociation
    {
        /// <summary>
        /// 关联ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 源记忆ID
        /// </summary>
        public string SourceMemoryId { get; set; } = string.Empty;

        /// <summary>
        /// 目标记忆ID
        /// </summary>
        public string TargetMemoryId { get; set; } = string.Empty;

        /// <summary>
        /// 关联类型
        /// </summary>
        public AssociationType Type { get; set; } = AssociationType.Semantic;

        /// <summary>
        /// 关联强度 (0.0 - 1.0)
        /// </summary>
        public double Strength { get; set; } = 0.5;

        /// <summary>
        /// 关联方向
        /// </summary>
        public AssociationDirection Direction { get; set; } = AssociationDirection.Bidirectional;

        /// <summary>
        /// 关联描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }

        /// <summary>
        /// 访问次数
        /// </summary>
        public int AccessCount { get; set; } = 0;

        /// <summary>
        /// 关联权重
        /// </summary>
        public double Weight { get; set; } = 1.0;

        /// <summary>
        /// 是否自动生成
        /// </summary>
        public bool IsAutoGenerated { get; set; } = false;

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 关联方向
    /// </summary>
    public enum AssociationDirection
    {
        /// <summary>
        /// 单向（源到目标）
        /// </summary>
        Unidirectional,

        /// <summary>
        /// 双向
        /// </summary>
        Bidirectional
    }

    /// <summary>
    /// 记忆图谱
    /// </summary>
    public class MemoryGraph
    {
        /// <summary>
        /// 图谱ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 中心记忆ID
        /// </summary>
        public string CenterMemoryId { get; set; } = string.Empty;

        /// <summary>
        /// 图谱深度
        /// </summary>
        public int Depth { get; set; } = 0;

        /// <summary>
        /// 节点列表
        /// </summary>
        public List<MemoryGraphNode> Nodes { get; set; } = new();

        /// <summary>
        /// 边列表
        /// </summary>
        public List<MemoryGraphEdge> Edges { get; set; } = new();

        /// <summary>
        /// 图谱统计信息
        /// </summary>
        public MemoryGraphStatistics Statistics { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 记忆图谱节点
    /// </summary>
    public class MemoryGraphNode
    {
        /// <summary>
        /// 节点ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 记忆ID
        /// </summary>
        public string MemoryId { get; set; } = string.Empty;

        /// <summary>
        /// 记忆对象
        /// </summary>
        public AgentMemory? Memory { get; set; }

        /// <summary>
        /// 节点层级
        /// </summary>
        public int Level { get; set; } = 0;

        /// <summary>
        /// 节点重要性
        /// </summary>
        public double Importance { get; set; } = 0.5;

        /// <summary>
        /// 连接度
        /// </summary>
        public int Degree { get; set; } = 0;

        /// <summary>
        /// 节点位置（用于可视化）
        /// </summary>
        public GraphPosition Position { get; set; } = new();

        /// <summary>
        /// 节点标签
        /// </summary>
        public List<string> Labels { get; set; } = new();
    }

    /// <summary>
    /// 记忆图谱边
    /// </summary>
    public class MemoryGraphEdge
    {
        /// <summary>
        /// 边ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 源节点ID
        /// </summary>
        public string SourceNodeId { get; set; } = string.Empty;

        /// <summary>
        /// 目标节点ID
        /// </summary>
        public string TargetNodeId { get; set; } = string.Empty;

        /// <summary>
        /// 关联对象
        /// </summary>
        public MemoryAssociation? Association { get; set; }

        /// <summary>
        /// 边权重
        /// </summary>
        public double Weight { get; set; } = 1.0;

        /// <summary>
        /// 边类型
        /// </summary>
        public string EdgeType { get; set; } = string.Empty;

        /// <summary>
        /// 边标签
        /// </summary>
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// 图形位置
    /// </summary>
    public class GraphPosition
    {
        /// <summary>
        /// X坐标
        /// </summary>
        public double X { get; set; } = 0.0;

        /// <summary>
        /// Y坐标
        /// </summary>
        public double Y { get; set; } = 0.0;

        /// <summary>
        /// Z坐标（3D图谱）
        /// </summary>
        public double Z { get; set; } = 0.0;
    }

    /// <summary>
    /// 记忆图谱统计信息
    /// </summary>
    public class MemoryGraphStatistics
    {
        /// <summary>
        /// 节点数量
        /// </summary>
        public int NodeCount { get; set; } = 0;

        /// <summary>
        /// 边数量
        /// </summary>
        public int EdgeCount { get; set; } = 0;

        /// <summary>
        /// 平均连接度
        /// </summary>
        public double AverageDegree { get; set; } = 0.0;

        /// <summary>
        /// 最大连接度
        /// </summary>
        public int MaxDegree { get; set; } = 0;

        /// <summary>
        /// 图谱密度
        /// </summary>
        public double Density { get; set; } = 0.0;

        /// <summary>
        /// 聚类系数
        /// </summary>
        public double ClusteringCoefficient { get; set; } = 0.0;

        /// <summary>
        /// 连通分量数量
        /// </summary>
        public int ConnectedComponents { get; set; } = 0;
    }
}
