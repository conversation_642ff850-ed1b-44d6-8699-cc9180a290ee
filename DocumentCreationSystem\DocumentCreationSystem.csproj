﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyTitle>文档管理及AI创作系统</AssemblyTitle>
    <AssemblyDescription>本地文档管理和AI辅助创作平台</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>DocumentCreationSystem.Program</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <!-- 基础框架 -->
    <PackageReference Include="AvalonEdit" Version="********" />
    <PackageReference Include="DocX" Version="4.0.25105.5786" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />

    <!-- 数据库依赖 -->
    <PackageReference Include="Microsoft.Data.Sqlite" Version="8.0.0" />

    <!-- 文档处理 -->
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.0.1" />
    <PackageReference Include="Markdig" Version="0.33.0" />

    <!-- UI组件 -->
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />


    <!-- HTTP客户端 -->
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="9.0.7" />

    <!-- 向量数据库客户端 (暂时注释掉，使用简化版本) -->
    <!-- <PackageReference Include="Qdrant.Client" Version="1.7.0" /> -->
  </ItemGroup>



</Project>
