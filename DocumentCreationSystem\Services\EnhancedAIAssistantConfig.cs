using System;
using System.Collections.Generic;
using System.Linq;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 增强AI助手配置类 - 管理不同AI模型的工具调用能力配置
    /// </summary>
    public class EnhancedAIAssistantConfig
    {
        /// <summary>
        /// 模型工具调用能力配置
        /// </summary>
        public static readonly Dictionary<string, ModelCapabilities> ModelCapabilities = new()
        {
            // OpenAI 模型
            ["gpt-4"] = new ModelCapabilities
            {
                SupportsFunctionCalling = true,
                SupportsSystemPrompts = true,
                MaxTokens = 8192,
                RecommendedPromptStyle = PromptStyle.Structured,
                ToolCallFormats = new[] { ToolCallFormat.NativeFunction, ToolCallFormat.TextBased }
            },
            ["gpt-3.5-turbo"] = new ModelCapabilities
            {
                SupportsFunctionCalling = true,
                SupportsSystemPrompts = true,
                MaxTokens = 4096,
                RecommendedPromptStyle = PromptStyle.Structured,
                ToolCallFormats = new[] { ToolCallFormat.NativeFunction, ToolCallFormat.TextBased }
            },

            // 智谱AI 模型
            ["GLM-4-Flash"] = new ModelCapabilities
            {
                SupportsFunctionCalling = false,
                SupportsSystemPrompts = true,
                MaxTokens = 8192,
                RecommendedPromptStyle = PromptStyle.Conversational,
                ToolCallFormats = new[] { ToolCallFormat.TextBased, ToolCallFormat.ActionBased }
            },
            ["GLM-4.1V-Thinking-Flash"] = new ModelCapabilities
            {
                SupportsFunctionCalling = false,
                SupportsSystemPrompts = true,
                MaxTokens = 8192,
                RecommendedPromptStyle = PromptStyle.ThinkingChain,
                ToolCallFormats = new[] { ToolCallFormat.TextBased, ToolCallFormat.ActionBased },
                SupportsThinkingChains = true
            },

            // DeepSeek 模型
            ["deepseek-chat"] = new ModelCapabilities
            {
                SupportsFunctionCalling = false,
                SupportsSystemPrompts = true,
                MaxTokens = 4096,
                RecommendedPromptStyle = PromptStyle.Detailed,
                ToolCallFormats = new[] { ToolCallFormat.TextBased, ToolCallFormat.ActionBased }
            },

            // LM Studio / Ollama 本地模型 (通用配置)
            ["local-model"] = new ModelCapabilities
            {
                SupportsFunctionCalling = false,
                SupportsSystemPrompts = true,
                MaxTokens = 2048,
                RecommendedPromptStyle = PromptStyle.Simple,
                ToolCallFormats = new[] { ToolCallFormat.TextBased, ToolCallFormat.ActionBased }
            }
        };

        /// <summary>
        /// 根据模型名称获取能力配置
        /// </summary>
        public static ModelCapabilities GetModelCapabilities(string modelName)
        {
            if (string.IsNullOrEmpty(modelName))
                return GetDefaultCapabilities();

            // 精确匹配
            if (ModelCapabilities.TryGetValue(modelName, out var capabilities))
                return capabilities;

            // 模糊匹配
            var lowerModelName = modelName.ToLower();
            var matchedKey = ModelCapabilities.Keys.FirstOrDefault(key => 
                lowerModelName.Contains(key.ToLower()) || key.ToLower().Contains(lowerModelName));

            if (matchedKey != null)
                return ModelCapabilities[matchedKey];

            // 根据模型名称推断能力
            return InferModelCapabilities(modelName);
        }

        /// <summary>
        /// 推断模型能力
        /// </summary>
        private static ModelCapabilities InferModelCapabilities(string modelName)
        {
            var lowerName = modelName.ToLower();

            // OpenAI 系列
            if (lowerName.Contains("gpt") || lowerName.Contains("openai"))
            {
                return new ModelCapabilities
                {
                    SupportsFunctionCalling = true,
                    SupportsSystemPrompts = true,
                    MaxTokens = 4096,
                    RecommendedPromptStyle = PromptStyle.Structured,
                    ToolCallFormats = new[] { ToolCallFormat.NativeFunction, ToolCallFormat.TextBased }
                };
            }

            // 智谱AI 系列
            if (lowerName.Contains("glm") || lowerName.Contains("zhipu"))
            {
                return new ModelCapabilities
                {
                    SupportsFunctionCalling = false,
                    SupportsSystemPrompts = true,
                    MaxTokens = 8192,
                    RecommendedPromptStyle = PromptStyle.Conversational,
                    ToolCallFormats = new[] { ToolCallFormat.TextBased, ToolCallFormat.ActionBased }
                };
            }

            // DeepSeek 系列
            if (lowerName.Contains("deepseek"))
            {
                return new ModelCapabilities
                {
                    SupportsFunctionCalling = false,
                    SupportsSystemPrompts = true,
                    MaxTokens = 4096,
                    RecommendedPromptStyle = PromptStyle.Detailed,
                    ToolCallFormats = new[] { ToolCallFormat.TextBased, ToolCallFormat.ActionBased }
                };
            }

            // 本地模型默认配置
            return GetDefaultCapabilities();
        }

        /// <summary>
        /// 获取默认能力配置
        /// </summary>
        private static ModelCapabilities GetDefaultCapabilities()
        {
            return new ModelCapabilities
            {
                SupportsFunctionCalling = false,
                SupportsSystemPrompts = true,
                MaxTokens = 2048,
                RecommendedPromptStyle = PromptStyle.Simple,
                ToolCallFormats = new[] { ToolCallFormat.TextBased, ToolCallFormat.ActionBased }
            };
        }

        /// <summary>
        /// 获取推荐的工具调用格式
        /// </summary>
        public static ToolCallFormat GetRecommendedToolCallFormat(string modelName)
        {
            var capabilities = GetModelCapabilities(modelName);
            return capabilities.ToolCallFormats.First();
        }

        /// <summary>
        /// 检查模型是否支持特定功能
        /// </summary>
        public static bool SupportsFeature(string modelName, ModelFeature feature)
        {
            var capabilities = GetModelCapabilities(modelName);
            return feature switch
            {
                ModelFeature.FunctionCalling => capabilities.SupportsFunctionCalling,
                ModelFeature.SystemPrompts => capabilities.SupportsSystemPrompts,
                ModelFeature.ThinkingChains => capabilities.SupportsThinkingChains,
                ModelFeature.LongContext => capabilities.MaxTokens > 4096,
                _ => false
            };
        }
    }

    /// <summary>
    /// 模型能力配置
    /// </summary>
    public class ModelCapabilities
    {
        /// <summary>
        /// 是否支持原生函数调用
        /// </summary>
        public bool SupportsFunctionCalling { get; set; } = false;

        /// <summary>
        /// 是否支持系统提示词
        /// </summary>
        public bool SupportsSystemPrompts { get; set; } = true;

        /// <summary>
        /// 最大令牌数
        /// </summary>
        public int MaxTokens { get; set; } = 2048;

        /// <summary>
        /// 推荐的提示词风格
        /// </summary>
        public PromptStyle RecommendedPromptStyle { get; set; } = PromptStyle.Simple;

        /// <summary>
        /// 支持的工具调用格式
        /// </summary>
        public ToolCallFormat[] ToolCallFormats { get; set; } = Array.Empty<ToolCallFormat>();

        /// <summary>
        /// 是否支持思维链
        /// </summary>
        public bool SupportsThinkingChains { get; set; } = false;
    }

    /// <summary>
    /// 提示词风格
    /// </summary>
    public enum PromptStyle
    {
        Simple,         // 简单直接
        Structured,     // 结构化
        Conversational, // 对话式
        Detailed,       // 详细说明
        ThinkingChain   // 思维链式
    }

    /// <summary>
    /// 工具调用格式
    /// </summary>
    public enum ToolCallFormat
    {
        NativeFunction, // 原生函数调用
        TextBased,      // 文本格式 [TOOL:name]params[/TOOL]
        ActionBased,    // 动作格式 [ACTION:description]
        JsonBased       // JSON格式
    }

    /// <summary>
    /// 模型功能特性
    /// </summary>
    public enum ModelFeature
    {
        FunctionCalling,
        SystemPrompts,
        ThinkingChains,
        LongContext
    }
}
